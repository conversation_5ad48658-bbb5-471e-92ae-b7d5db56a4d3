# Web Development Task Catalog

*Think of This Like:*

• *Request = What your manager/client/team asked you to do.*
• *Deliverable = The actual thing you created in response.*
• You're showing how a real task starts and ends — useful for teaching an AI how to help others with similar jobs.

---

## ✅ What You Need to Submit

### 1. *Occupation*

**Software Engineer – Web Development**

---

### 2. *Associated Actions*

**Primary Actions Covered in This Catalog:**

• **Build features** - API endpoints, UI components, marketplace functionality
• **Fix bugs** - Debug payment issues, resolve race conditions
• **Write documentation** - Environment setup guides, performance reports
• **Analyze performance** - Profile APIs, optimize database queries
• **Debug backend features** - Payment flows, authentication systems
• **Deploy and test** - Staging deployments, end-to-end testing

---

### 3. 📝 *Request (what you were asked to do)*

This is the instruction you received. Make sure it includes:

• *Goal* – Why are you doing this?
• *Deliverable Ask* – What exactly do you need to produce? In what format?
• *Context* – Anything someone else would need to complete this. Think of links, login info, API docs, or tech constraints.

---

## Task Examples

### Task 1: Implement Agent–Landlord Instant Decision API

**Associated Action:** Build backend features

*Goal*: Speed up deal closures by allowing landlords to instantly approve or deny tenant applications.

*Deliverable Ask*: Create a REST API endpoint (`POST /api/v1/agents/applications/:id/decision`) that updates the application status and triggers notifications. Include unit and integration tests.

*Context*: Backend is built with NestJS. Use existing authentication and notification modules. See `/Backend/src/modules/auth/` and `/Backend/src/api/v1/agents/`. Must handle concurrent requests and maintain data consistency.

---

### Task 2: Debug Escrow Double-Charge Bug

**Associated Action:** Fix bugs

*Goal*: Ensure escrow payments are only processed once per transaction.

*Deliverable Ask*: Identify and fix the bug causing double-charging in the escrow flow. Add regression tests and document the fix in a PDF post-mortem.

*Context*: Bug reported during QA. Payment logic is in `/Backend/src/modules/payments/` and `/Backend/src/api/v1/payments/`. Critical production issue affecting customer billing.

---

### Task 3: Write Frontend Environment Setup Guide

**Associated Action:** Write documentation

*Goal*: Reduce onboarding time for new frontend developers.

*Deliverable Ask*: Write a Markdown guide covering prerequisites, installation, environment variables, and troubleshooting. Review with a new team member.

*Context*: Frontend uses React, TypeScript, and Vite. Place the guide in `/Frontend/docs/ENVIRONMENT_SETUP.md`. New developers currently take 2+ days to get environment working.

---

### Task 4: Optimize Property Search API Performance

**Associated Action:** Analyze performance

*Goal*: Reduce response time for property search under high load.

*Deliverable Ask*: Profile the `/api/v1/properties/search` endpoint, implement optimizations (e.g., query indexing, caching), and document before/after metrics in a Markdown report.

*Context*: Backend uses PostgreSQL and TypeORM. See `/Backend/src/api/v1/properties/` and `/Backend/database/`. Current response time is 2.5s with 1000+ concurrent users.

---

### Task 5: Launch Service Bundle Marketplace Feature

**Associated Action:** Deploy and test

*Goal*: Enable tenants to book bundled services (cleaning, WiFi, decor) via the platform.

*Deliverable Ask*: Deploy the new marketplace UI to staging, write end-to-end Cypress tests, and prepare a release note in Markdown.

*Context*: Frontend code in `/Frontend/src/components/marketplace/`. Use `/Frontend/tests/e2e/` for Cypress tests. Feature must be ready for production launch next sprint.

---

### 4. 📦 *Deliverable (the thing you produced)*

Submit:

• A *real* output (code, report, doc, audit, deck, etc.)
• If it's code, upload the file or paste the text
• Write an *intro* like:

"Attached is the mobile performance audit for `/pricing`, `/features`, and `/signup`. I used Lighthouse and WebPageTest. The document includes key metrics, screenshots, and prioritized recommendations."

---

## Deliverables

### Task 1: Agent Decision API
*Intro*: "Attached is the REST API implementation for instant agent-landlord decision making. I created the endpoint with proper authentication, validation, and comprehensive test coverage. The code follows NestJS best practices and includes integration tests."

**Deliverable Files:**
- [GitHub PR #123 – Agent Decision API](https://github.com/ptownmoving/backend/pull/123)
- [API Documentation](docs/agent-decision-api.md)
- [Test Coverage Report](coverage/agent-decision-tests.html)

### Task 2: Escrow Bugfix
*Intro*: "Attached is the bugfix for the double-charge issue in the escrow payment flow. I identified the race condition, implemented proper locking, and added regression tests. The post-mortem document includes root cause analysis and prevention measures."

**Deliverable Files:**
- [GitHub PR #124 – Escrow Bugfix](https://github.com/ptownmoving/backend/pull/124)
- [PDF Post-Mortem](docs/escrow-bugfix-postmortem.pdf)
- [Regression Test Suite](tests/escrow-regression.spec.ts)

### Task 3: Environment Setup Guide
*Intro*: "Attached is the comprehensive frontend environment setup guide. I documented all prerequisites, installation steps, common issues, and troubleshooting tips. The guide has been reviewed by a new team member and includes screenshots."

**Deliverable Files:**
- [ENVIRONMENT_SETUP.md](Frontend/docs/ENVIRONMENT_SETUP.md)
- [Setup Validation Script](scripts/validate-environment.sh)
- [Review Feedback](docs/environment-guide-review.md)

### Task 4: Search Performance Optimization
*Intro*: "Attached is the performance optimization report for the property search API. I profiled the endpoint, identified bottlenecks, implemented query optimizations and caching, and documented before/after metrics with detailed recommendations."

**Deliverable Files:**
- [GitHub PR #126 – Search Optimization](https://github.com/ptownmoving/backend/pull/126)
- [Performance Report](docs/property-search-performance.md)
- [Load Test Results](tests/performance/search-load-test-results.json)
- [Database Index Migration](migrations/add-search-indexes.sql)

### Task 5: Marketplace Feature Launch
*Intro*: "Attached is the service bundle marketplace feature implementation. I deployed the UI to staging, wrote comprehensive end-to-end tests, and prepared release documentation. The feature includes booking, payment, and rating functionality."

**Deliverable Files:**
- [GitHub PR #127 – Marketplace Feature](https://github.com/ptownmoving/frontend/pull/127)
- [Release Note](docs/marketplace-release-note.md)
- [Cypress E2E Tests](tests/e2e/marketplace.cy.ts)
- [Staging Deployment Log](deployments/staging-marketplace-deploy.log)

---

### 5. 🕓 Time to Complete

| Task | Time (hours) |
|------|--------------|
| 1    | 3.0          |
| 2    | 2.0          |
| 3    | 1.5          |
| 4    | 2.5          |
| 5    | 3.0          |

---

### 6. 🎯 Career Phase

| Task | Phase  |
|------|--------|
| 1    | mid    |
| 2    | mid    |
| 3    | early  |
| 4    | senior |
| 5    | mid    |

---

### 7. 🔁 How Representative Is This Task?

| Task | Scale (1–5) |
|------|-------------|
| 1    | 5           |
| 2    | 4           |
| 3    | 3           |
| 4    | 4           |
| 5    | 5           |

---

### 8. 🔥 How Difficult Is This Task?

| Task | Scale (1–5) | Reasoning |
|------|-------------|-----------|
| 1    | 3           | Standard API development with existing patterns |
| 2    | 4           | Complex debugging, race conditions, production impact |
| 3    | 2           | Straightforward documentation task |
| 4    | 5           | Performance optimization requires deep system knowledge |
| 5    | 4           | Feature deployment with multiple integration points |

---

## 📊 **Task Summary Metrics**

| Metric | Average | Range |
|--------|---------|-------|
| **Time to Complete** | 2.4 hours | 1.5 - 3.0 hours |
| **Difficulty** | 3.6/5 | 2 - 5 |
| **Representativeness** | 4.2/5 | 3 - 5 |

**Career Phase Distribution:**
- Early: 20% (1 task)
- Mid: 60% (3 tasks)
- Senior: 20% (1 task)

---

**End of Catalog**