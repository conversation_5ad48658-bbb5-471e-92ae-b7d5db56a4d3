import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Toaster } from '@/components/ui/toaster';
import { Toaster as Sonner } from '@/components/ui/sonner';
import { TooltipProvider } from '@/components/ui/tooltip';
import Index from './pages/Index';
import Properties from './pages/Properties';
import Auth from './pages/Auth';
import Contact from './pages/Contact';
import UserProfile from './pages/UserProfile';
import Search from './pages/Search';
import Messages from './pages/Messages';
import PropertyManagement from './pages/PropertyManagement';
import TenantPortal from './pages/TenantPortal';
import LandlordPortal from './pages/LandlordPortal';
import AgentDashboard from './pages/AgentDashboard';
import EnhancedAgentDashboard from './pages/EnhancedAgentDashboard';
import AdminDashboard from './pages/AdminDashboard';
import AdminSeedData from './pages/AdminSeedData';
import AdvancedFeatures from './pages/AdvancedFeatures';
import AdvancedFeaturesTest from './pages/AdvancedFeaturesTest';
import PaymentDashboard from './pages/PaymentDashboard';
import ScalingOptimization from './pages/ScalingOptimization';
import AdvancedBusinessLogic from './pages/AdvancedBusinessLogic';
import RentalApplication from './pages/RentalApplication';
import VerificationStatus from './pages/VerificationStatus';
import Escrow from './pages/Escrow';
import PaymentCallback from './pages/PaymentCallback';
import PaymentDebug from './pages/PaymentDebug';
import ReceiptDemo from './pages/ReceiptDemo';
import PaymentTest from './pages/PaymentTest';
import AppStatus from './pages/AppStatus';
import CreateAlert from './pages/CreateAlert';
import ContactAgent from './pages/ContactAgent';
import MaintenanceDashboard from './pages/MaintenanceDashboard';
import AnalyticsDashboardPage from './pages/AnalyticsDashboardPage';
import CommunicationDashboardPage from './pages/CommunicationDashboardPage';
import PerformanceDashboardPage from './pages/PerformanceDashboardPage';
import SecurityDashboardPage from './pages/SecurityDashboardPage';
import NotFound from './pages/NotFound';
import ErrorBoundary from './components/error/ErrorBoundary';
import PageErrorBoundary from './components/error/PageErrorBoundary';

// Simple test component
const SimpleHome = () => (
  <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
    <h1 style={{ color: '#333', marginBottom: '20px' }}>
      PHCityRent - Port Harcourt Real Estate Platform
    </h1>
    <p style={{ color: '#666', fontSize: '16px', lineHeight: '1.6' }}>
      Welcome to PHCityRent! This is a simplified version to test the React application.
    </p>
    <div
      style={{
        marginTop: '20px',
        padding: '15px',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px',
      }}
    >
      <h2 style={{ color: '#28a745', marginBottom: '10px' }}>✅ Application Status</h2>
      <ul style={{ color: '#666' }}>
        <li>✅ React is working</li>
        <li>✅ Routing is functional</li>
        <li>✅ UI components are loading</li>
        <li>✅ Development server is running</li>
      </ul>
    </div>
    <div style={{ marginTop: '20px' }}>
      <button
        style={{
          padding: '10px 20px',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '16px',
        }}
        onClick={() => alert('PHCityRent is working!')}
      >
        Test Interaction
      </button>
    </div>
  </div>
);

function App() {
  return (
    <ErrorBoundary level="critical" showDetails={process.env.NODE_ENV === 'development'}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <Routes>
          <Route
            path="/"
            element={
              <PageErrorBoundary pageName="Home">
                <Index />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/properties"
            element={
              <PageErrorBoundary pageName="Properties">
                <Properties />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/auth"
            element={
              <PageErrorBoundary pageName="Authentication">
                <Auth />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/admin/seed-data"
            element={
              <PageErrorBoundary pageName="Admin Seed Data">
                <AdminSeedData />
              </PageErrorBoundary>
            }
          />
          <Route
            path="/contact"
            element={
              <PageErrorBoundary pageName="Contact">
                <Contact />
              </PageErrorBoundary>
            }
          />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </TooltipProvider>
    </ErrorBoundary>
  );
}

export default App;
