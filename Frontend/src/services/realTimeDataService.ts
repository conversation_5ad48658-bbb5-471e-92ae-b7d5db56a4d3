import { io, Socket } from 'socket.io-client';

// Types for real-time data
export interface AgentMetrics {
  agentId: string;
  totalProperties: number;
  activeClients: number;
  monthlyEarnings: number;
  conversionRate: number;
  responseTime: number;
  clientSatisfaction: number;
  dealsCompleted: number;
  lastUpdated: string;
}

export interface PropertyUpdate {
  propertyId: string;
  status: 'available' | 'rented' | 'maintenance' | 'pending';
  viewsCount: number;
  inquiriesCount: number;
  lastViewed: string;
  priceChanges: Array<{
    oldPrice: number;
    newPrice: number;
    changedAt: string;
  }>;
}

export interface PaymentAlert {
  transactionId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  amount: number;
  propertyId: string;
  agentId: string;
  tenantId: string;
  paymentMethod: string;
  timestamp: string;
}

export interface DashboardMetrics {
  userId: string;
  totalRevenue: number;
  activeProperties: number;
  pendingPayments: number;
  newInquiries: number;
  conversionRate: number;
  growthRate: number;
  lastUpdated: string;
}

// Event types for subscriptions
export type RealTimeEventType = 
  | 'agent_metrics'
  | 'property_updates'
  | 'payment_alerts'
  | 'dashboard_metrics'
  | 'system_notifications';

export type RealTimeCallback<T = any> = (data: T) => void;

interface Subscription {
  id: string;
  eventType: RealTimeEventType;
  callback: RealTimeCallback;
  filter?: Record<string, any>;
}

interface ConnectionState {
  isConnected: boolean;
  isConnecting: boolean;
  lastConnected: Date | null;
  reconnectAttempts: number;
  connectionId: string | null;
}

/**
 * Singleton service for managing real-time data subscriptions
 * Integrates with traditional backend WebSocket connections
 */
export class RealTimeDataService {
  private static instance: RealTimeDataService;
  private socket: Socket | null = null;
  private subscriptions: Map<string, Subscription> = new Map();
  private connectionState: ConnectionState = {
    isConnected: false,
    isConnecting: false,
    lastConnected: null,
    reconnectAttempts: 0,
    connectionId: null,
  };
  private maxReconnectAttempts: number = 5;
  private reconnectDelay: number = 1000;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private reconnectTimeout: NodeJS.Timeout | null = null;

  private constructor() {
    this.initializeConnection();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): RealTimeDataService {
    if (!RealTimeDataService.instance) {
      RealTimeDataService.instance = new RealTimeDataService();
    }
    return RealTimeDataService.instance;
  }

  /**
   * Initialize real-time connection
   */
  private async initializeConnection(): Promise<void> {
    if (this.connectionState.isConnecting || this.connectionState.isConnected) {
      return;
    }

    try {
      this.connectionState.isConnecting = true;

      // Get JWT token from localStorage or auth service
      const token = this.getAuthToken();
      if (!token) {
        console.warn('No authentication token available for real-time connection');
        this.connectionState.isConnecting = false;
        return;
      }

      // Create Socket.IO connection
      const backendUrl = this.getBackendUrl();
      this.socket = io(`${backendUrl}/realtime`, {
        auth: { token },
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: true,
      });

      this.setupSocketEventHandlers();
      this.socket.connect();

    } catch (error) {
      console.error('Failed to initialize real-time connection:', error);
      this.handleConnectionError();
    }
  }

  /**
   * Setup Socket.IO event handlers
   */
  private setupSocketEventHandlers(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Real-time data service connected');
      this.connectionState = {
        isConnected: true,
        isConnecting: false,
        lastConnected: new Date(),
        reconnectAttempts: 0,
        connectionId: this.socket?.id || null,
      };
      this.startHeartbeat();
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Real-time data service disconnected:', reason);
      this.connectionState.isConnected = false;
      this.stopHeartbeat();
      
      if (reason === 'io server disconnect') {
        // Server disconnected us, try to reconnect
        this.handleConnectionError();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('Real-time connection error:', error);
      this.handleConnectionError();
    });

    // Handle real-time events
    this.socket.on('agent_metrics', (data: AgentMetrics) => {
      this.handleAgentMetricsUpdate(data);
    });

    this.socket.on('property_updates', (data: PropertyUpdate) => {
      this.handlePropertyUpdate(data);
    });

    this.socket.on('payment_alerts', (data: PaymentAlert) => {
      this.handlePaymentAlert(data);
    });

    this.socket.on('dashboard_metrics', (data: DashboardMetrics) => {
      this.handleDashboardMetricsUpdate(data);
    });

    this.socket.on('system_notifications', (data: any) => {
      this.handleSystemNotification(data);
    });

    this.socket.on('connected', (data: any) => {
      console.log('Real-time service confirmed connection:', data);
    });

    this.socket.on('pong', (data: any) => {
      // Heartbeat response received
    });
  }

  /**
   * Handle connection errors with exponential backoff
   */
  private handleConnectionError(): void {
    this.connectionState.isConnecting = false;
    
    if (
      this.connectionState.reconnectAttempts < this.maxReconnectAttempts
    ) {
      this.connectionState.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(
        2, this.connectionState.reconnectAttempts - 1
      );
      
      console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.connectionState.reconnectAttempts})`);
      
      this.reconnectTimeout = setTimeout(() => {
        this.initializeConnection();
      }, delay);
    } else {
      console.error('Max reconnection attempts reached');
      this.connectionState.isConnected = false;
    }
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.socket && this.connectionState.isConnected) {
        this.socket.emit('ping');
      }
    }, 30000); // Send ping every 30 seconds
  }

  /**
   * Stop heartbeat
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Subscribe to real-time agent metrics
   */
  public subscribeToAgentMetrics(
    agentId: string, 
    callback: RealTimeCallback<AgentMetrics>
  ): string {
    const subscriptionId = `agent_metrics_${agentId}_${Date.now()}`;
    
    this.subscriptions.set(subscriptionId, {
      id: subscriptionId,
      eventType: 'agent_metrics',
      callback,
      filter: { agentId: agentId }
    });

    // Send subscription request to server
    this.sendSubscriptionRequest('agent_metrics', { agentId: agentId }, subscriptionId);

    return subscriptionId;
  }

  /**
   * Subscribe to real-time property updates
   */
  public subscribeToPropertyUpdates(
    propertyId: string,
    callback: RealTimeCallback<PropertyUpdate>
  ): string {
    const subscriptionId = `property_updates_${propertyId}_${Date.now()}`;
    
    this.subscriptions.set(subscriptionId, {
      id: subscriptionId,
      eventType: 'property_updates',
      callback,
      filter: { property_id: propertyId }
    });

    // Send subscription request to server
    this.sendSubscriptionRequest('property_updates', { property_id: propertyId }, subscriptionId);

    return subscriptionId;
  }

  /**
   * Subscribe to real-time payment status updates
   */
  public subscribeToPaymentStatus(
    transactionId: string,
    callback: RealTimeCallback<PaymentAlert>
  ): string {
    const subscriptionId = `payment_status_${transactionId}_${Date.now()}`;
    
    this.subscriptions.set(subscriptionId, {
      id: subscriptionId,
      eventType: 'payment_alerts',
      callback,
      filter: { transaction_id: transactionId }
    });

    // Send subscription request to server
    this.sendSubscriptionRequest('payment_alerts', { transaction_id: transactionId }, subscriptionId);

    return subscriptionId;
  }

  /**
   * Subscribe to real-time dashboard metrics
   */
  public subscribeToDashboardMetrics(
    userId: string,
    callback: RealTimeCallback<DashboardMetrics>
  ): string {
    const subscriptionId = `dashboard_metrics_${userId}_${Date.now()}`;
    
    this.subscriptions.set(subscriptionId, {
      id: subscriptionId,
      eventType: 'dashboard_metrics',
      callback,
      filter: { user_id: userId }
    });

    // Send subscription request to server
    this.sendSubscriptionRequest(
      'dashboard_metrics', { user_id: userId }, subscriptionId
    );

    return subscriptionId;
  }

  /**
   * Send subscription request to server
   */
  private sendSubscriptionRequest(
    eventType: string, 
    filters: Record<string, any>, 
    subscriptionId: string
  ): void {
    if (this.socket && this.connectionState.isConnected) {
      this.socket.emit('subscribe', {
        eventType,
        filters,
        subscriptionId,
      });
    } else {
      console.warn('Socket not connected, subscription request queued');
      // Could implement a queue here for when connection is restored
    }
  }

  /**
   * Unsubscribe from real-time updates
   */
  public unsubscribe(subscriptionId: string): void {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {
      // Send unsubscribe request to server
      if (this.socket && this.connectionState.isConnected) {
        this.socket.emit('unsubscribe', { subscriptionId });
      }
      
      this.subscriptions.delete(subscriptionId);
      console.log(`Unsubscribed from ${subscriptionId}`);
    }
  }

  /**
   * Unsubscribe from all subscriptions
   */
  public unsubscribeAll(): void {
    this.subscriptions.forEach((subscription, subscriptionId) => {
      this.unsubscribe(subscriptionId);
    });
    
    this.subscriptions.clear();
    console.log('Unsubscribed from all real-time updates');
  }

  /**
   * Get connection status
   */
  public getConnectionStatus(): boolean {
    return this.connectionState.isConnected;
  }

  /**
   * Get active subscriptions count
   */
  public getActiveSubscriptionsCount(): number {
    return this.subscriptions.size;
  }

  /**
   * Get connection state
   */
  public getConnectionState(): ConnectionState {
    return { ...this.connectionState };
  }

  /**
   * Disconnect from real-time service
   */
  public disconnect(): void {
    this.stopHeartbeat();
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    
    this.connectionState = {
      isConnected: false,
      isConnecting: false,
      lastConnected: null,
      reconnectAttempts: 0,
      connectionId: null,
    };
    
    console.log('Real-time data service disconnected');
  }

  // Event handlers
  private handleAgentMetricsUpdate(data: AgentMetrics): void {
    this.subscriptions.forEach((subscription) => {
      if (subscription.eventType === 'agent_metrics' && 
          subscription.filter?.agentId === data.agentId) {
        subscription.callback(data);
      }
    });
  }

  private handlePropertyUpdate(data: PropertyUpdate): void {
    this.subscriptions.forEach((subscription) => {
      if (subscription.eventType === 'property_updates' && 
          subscription.filter?.propertyId === data.propertyId) {
        subscription.callback(data);
      }
    });
  }

  private handlePaymentAlert(data: PaymentAlert): void {
    this.subscriptions.forEach((subscription) => {
      if (subscription.eventType === 'payment_alerts' && 
          subscription.filter?.transactionId === data.transactionId) {
        subscription.callback(data);
      }
    });
  }

  private handleDashboardMetricsUpdate(data: DashboardMetrics): void {
    this.subscriptions.forEach((subscription) => {
      if (subscription.eventType === 'dashboard_metrics' && 
          subscription.filter?.userId === data.userId) {
        subscription.callback(data);
      }
    });
  }

  private handleSystemNotification(data: any): void {
    this.subscriptions.forEach((subscription) => {
      if (subscription.eventType === 'system_notifications') {
        subscription.callback(data);
      }
    });
  }

  /**
   * Get authentication token
   */
  private getAuthToken(): string | null {
    // Try to get token from localStorage
    const token = localStorage.getItem('auth_token') || 
                  localStorage.getItem('access_token') ||
                  sessionStorage.getItem('auth_token');
    
    return token;
  }

  /**
   * Get backend URL
   */
  private getBackendUrl(): string {
    // In development, use localhost
    if (process.env.NODE_ENV === 'development') {
      return 'http://localhost:3001';
    }
    
    // In production, use environment variable or default
    return process.env.REACT_APP_BACKEND_URL || 
           process.env.VITE_API_BASE_URL || 
           'https://api.phcityrent.com';
  }
}

// Export singleton instance
export const realTimeDataService = RealTimeDataService.getInstance();
