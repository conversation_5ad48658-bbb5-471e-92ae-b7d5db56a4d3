import { MigrationInterface, QueryRunner } from 'typeorm';

export class 016bfc833bf7491dA41718a92898bde220250615192809 implements MigrationInterface {
  name = '016bfc833bf7491dA41718a92898bde220250615192809';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Converted from Supabase migration: 016bfc833bf7491dA41718a92898bde2
    await queryRunner.query(`CREATE TABLE public.rental_applications (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users NOT NULL,
  property_id UUID REFERENCES public.properties,
  application_data JSONB NOT NULL DEFAULT '{}',
  status TEXT NOT NULL DEFAULT 'submitted',
  submitted_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
)`);
    await queryRunner.query(`ALTER TABLE public.rental_applications ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`CREATE POLICY "Users can view their own rental applications" 
  ON public.rental_applications 
  FOR SELECT 
  USING (auth.uid() = user_id)`);
    await queryRunner.query(`CREATE POLICY "Users can create their own rental applications" 
  ON public.rental_applications 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id)`);
    await queryRunner.query(`CREATE POLICY "Users can update their own rental applications" 
  ON public.rental_applications 
  FOR UPDATE 
  USING (auth.uid() = user_id)`);
    await queryRunner.query(`CREATE TABLE public.messages (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  sender_id UUID REFERENCES users NOT NULL,
  recipient_id UUID REFERENCES users NOT NULL,
  property_id UUID REFERENCES public.properties,
  subject TEXT,
  content TEXT NOT NULL,
  is_read BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
)`);
    await queryRunner.query(`ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY`);
    await queryRunner.query(`CREATE POLICY "Users can view their own messages" 
  ON public.messages 
  FOR SELECT 
  USING (auth.uid() = sender_id OR auth.uid() = recipient_id)`);
    await queryRunner.query(`CREATE POLICY "Users can send messages" 
  ON public.messages 
  FOR INSERT 
  WITH CHECK (auth.uid() = sender_id)`);
    await queryRunner.query(`CREATE POLICY "Users can update received messages" 
  ON public.messages 
  FOR UPDATE 
  USING (auth.uid() = recipient_id)`);
    await queryRunner.query(`CREATE TRIGGER handle_rental_applications_updated_at
    BEFORE UPDATE ON public.rental_applications
    FOR EACH ROW
    EXECUTE PROCEDURE public.handle_updated_at()`);
    await queryRunner.query(`CREATE TRIGGER handle_messages_updated_at
    BEFORE UPDATE ON public.messages
    FOR EACH ROW
    EXECUTE PROCEDURE public.handle_updated_at()`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Rollback queries (auto-generated)
    await queryRunner.query(`DROP TABLE IF EXISTS public.messages CASCADE`);
    await queryRunner.query(`DROP TABLE IF EXISTS public.rental_applications CASCADE`);
  }
}
